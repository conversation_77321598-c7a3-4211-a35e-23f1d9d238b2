﻿using Core.Utilities.IoC;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using FluentValidation;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Linq;

namespace Business.ValidationRules.FluentValidation
{
    public class MemberValidator : AbstractValidator<Member>
    {
        private readonly ICompanyContext _companyContext;

        public MemberValidator()
        {
            _companyContext = ServiceTool.ServiceProvider.GetService<ICompanyContext>();

            RuleFor(x => x.Name).NotEmpty().WithMessage("Ad Soyad kısmı boş bırakılamaz.");
            RuleFor(x => x.Gender).NotEmpty().WithMessage("Cinsiyet kısmı boş bırakılamaz.");
            RuleFor(x => x.PhoneNumber).NotEmpty().WithMessage("Telefon numarası kısmı boş bırakılamaz.");
            RuleFor(p => p.PhoneNumber).Must((member, phone) => BeUniquePhoneNumber(member)).WithMessage("Bu telefon numarası bu salonda zaten kayıtlı.");
            RuleFor(p => p.PhoneNumber).Must(StartsWithZero).WithMessage("Telefon numarası 0 ile başlamak zorundadır");
            RuleFor(p => p.PhoneNumber).Length(11).WithMessage("Telefon numarasını kontrol ediniz.");

            // E-posta adresi için validasyon kuralları
            RuleFor(p => p.Email).EmailAddress().When(p => !string.IsNullOrEmpty(p.Email)).WithMessage("Geçerli bir e-posta adresi giriniz.");
            RuleFor(p => p.Email).Must((member, email) => BeUniqueEmailInCompany(member)).When(p => !string.IsNullOrEmpty(p.Email)).WithMessage("Bu e-posta adresi bu salonda başka bir üye için zaten kullanılıyor.");
        }

        private bool BeUniquePhoneNumber(Member member)
        {
            // Database validation kaldırıldı - Business layer'da kontrol edilecek
            return true;
        }

        private bool StartsWithZero(string arg)
        {
            return arg.StartsWith("0");
        }

        private bool BeUniqueEmailInCompany(Member member)
        {
            // Database validation kaldırıldı - Business layer'da kontrol edilecek
            return true;
        }
    }
}