using Entities.Concrete;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.ValidationRules.FluentValidation
{
   public class CompanyAdressValidator:AbstractValidator<CompanyAdress>
    {
        public CompanyAdressValidator()
        {
            RuleFor(c => c.Adress).NotEmpty().WithMessage("Adres boş bırakılamaz.");
            RuleFor(c => c.CompanyID).NotEmpty().WithMessage("Şirket ismi boş bırakılamaz.");
            RuleFor(c => c.CityID).NotEmpty().WithMessage("İl kısmı boş bırakılamaz.");
            RuleFor(c => c.TownID).NotEmpty().WithMessage("İlçe kısmı boş bırakılamaz.");
            // Database validation kaldırıldı - Business layer'da kontrol edilecek
        }
    }
}
