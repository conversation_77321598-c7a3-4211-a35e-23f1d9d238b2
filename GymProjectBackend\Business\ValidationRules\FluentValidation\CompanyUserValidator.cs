using Core.Entities.Concrete;
using FluentValidation;
using System.Linq;

namespace Business.ValidationRules.FluentValidation
{
    public class CompanyUserValidator : AbstractValidator<CompanyUser>
    {
        public CompanyUserValidator()
        {
            RuleFor(p => p.Name).NotEmpty().WithMessage("İsim kısmı boş bırakılamaz.");
            RuleFor(p => p.PhoneNumber).NotEmpty().WithMessage("Telefon kısmı boş bırakılamaz.");
            RuleFor(p => p.PhoneNumber).Length(11).WithMessage("Telefon numarasını kontrol ediniz.");
            RuleFor(p => p.PhoneNumber).Must(StartsWithZero).WithMessage("Telefon numarası 0 ile başlamak zorundadır");
            RuleFor(x => x.Email).EmailAddress().WithMessage("E Posta adresini doğru giriniz.");
            RuleFor(p => p.CityID).NotEmpty().WithMessage("İl kısmı boş bırakılamaz.");
            RuleFor(p => p.TownID).NotEmpty().WithMessage("İlçe kısmı boş bırakılamaz.");
            // Database validation kaldırıldı - Business layer'da kontrol edilecek
        }

        private bool StartsWithZero(string arg)
        {
            return arg.StartsWith("0");
        }
    }
}